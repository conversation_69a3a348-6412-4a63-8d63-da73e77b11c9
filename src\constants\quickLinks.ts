import {
  PlusCircle,
  ArrowRightLeft,
  Clock,
  AlertTriangle,
  ListPlus,
  Search,
  BarChart4,
  Plus,
  MapPin,
  RefreshCw,
  FileText
} from 'lucide-react';
import { LucideIcon } from 'lucide-react';

/**
 * Interface for quick link items available in the application
 */
export interface QuickLinkItem {
  /** Unique identifier for the quick link */
  id: string;
  /** URL path the quick link navigates to */
  path: string;
  /** Display label for the quick link */
  label: string;
  /** Icon component to display with the quick link */
  icon: LucideIcon;
  /** Description of what the quick link does */
  description: string;
}

/**
 * Map of icon names to their corresponding Lucide icon components
 */
export const QUICK_LINK_ICONS: Record<string, LucideIcon> = {
  'add-item': PlusCircle,
  'expiring-items': Clock,
  'expired-items': AlertTriangle,
  'export-data': ArrowRightLeft,
  'batch-add': ListPlus,
  'low-stock': BarChart4,
  'search': Search,
  'refresh': RefreshCw,
  'import-csv': FileText,
  'mapPin': MapPin,
  'plus': Plus
};

/**
 * All available quick links that users can choose from
 */
export const AVAILABLE_QUICK_LINKS: QuickLinkItem[] = [
  {
    id: 'add-item',
    path: '/add-item',
    label: 'Add Item',
    icon: PlusCircle,
    description: 'Create a new inventory item'
  },
  {
    id: 'expiring-items',
    path: '/items?filter=expiring',
    label: 'Expiring Items',
    icon: Clock,
    description: 'View items that will expire soon'
  },
  {
    id: 'expired-items',
    path: '/items?filter=expired',
    label: 'Expired Items',
    icon: AlertTriangle,
    description: 'View items that have already expired'
  },
  {
    id: 'export-data',
    path: '/settings?tab=data',
    label: 'Export Data',
    icon: ArrowRightLeft,
    description: 'Export your inventory data'
  },
  {
    id: 'batch-add',
    path: '/batch-add-items',
    label: 'Batch Add',
    icon: ListPlus,
    description: 'Add multiple items at once'
  },
  {
    id: 'low-stock',
    path: '/items?filter=low-stock',
    label: 'Low Stock',
    icon: BarChart4,
    description: 'View items with low stock levels'
  },
  {
    id: 'search',
    path: '/items?search=true',
    label: 'Search Items',
    icon: Search,
    description: 'Search for specific items'
  },
  {
    id: 'refresh',
    path: '#refresh',
    label: 'Refresh',
    icon: RefreshCw,
    description: 'Refresh the browser'
  },
  {
    id: 'import-csv',
    path: '/settings?tab=data',
    label: 'Import CSV',
    icon: FileText,
    description: 'Import inventory data from CSV file'
  }
];

/**
 * Default quick links to use if user hasn't customized them
 */
export const DEFAULT_QUICK_LINKS = [
  'add-item',
  'expiring-items',
  'expired-items',
  'export-data'
];

/**
 * Maximum number of quick links a user can select
 */
export const MAX_QUICK_LINKS = 10;

/**
 * Local storage key for quick links
 */
export const QUICK_LINKS_STORAGE_KEY = 'quick-links';
